import React, { useState, useEffect, useRef, useMemo } from "react";
import { AgGridReact } from "ag-grid-react";
import {
  ColDef,
  GridReadyEvent,
  CellStyle,
  ICellRendererParams,
} from "ag-grid-community";
import "ag-grid-community/styles/ag-grid.css";
import "ag-grid-community/styles/ag-theme-alpine.css";
import "ag-grid-community/styles/ag-theme-quartz.css";
import { toast } from "react-toastify";
import axios from "axios";

import { FlatpickrThree } from "../../../../Helper/FlatpickrThree";
import ReactSelect from "../../../../Helper/ReactSelect";
import {
  LoadingOverlay,
  NoRowsOverlay,
} from "../../../../Helper/AgGridOverlays";
import { useAuth } from "../../../../modules/auth";
import {
  getTripHistoryData,
  getTripHistoryShiftTime,
  getTripHistoryFilter,
  getRosterDetails,
  resetEscort,
} from "./TripHistoryRequests";
import {
  TripHistoryDataResponse,
  ShiftTime,
  TripHistoryRecord,
} from "./TripHistoryModels";
import EmployeeListModal, { EmployeeListModalRef } from "./EmployeeListModal";
import EmployeeRosterModal, {
  EmployeeRosterModalRef,
} from "./EmployeeRosterModal";
import EscortModal, { EscortModalRef } from "./EscortModal";
import ResetModal, { ResetModalRef } from "./ResetModal";
import CabAllotModal, { CabAllotModalRef } from "./CabAllotModal";

const TripHistory: React.FC = () => {
  const gridRef = useRef<AgGridReact>(null);
  const [loading, setLoading] = useState(false);
  const [networkLoader, setNetworkLoader] = useState(true);
  const [showGrid, setShowGrid] = useState(false);
  const { logout } = useAuth();
  const [error, setError] = useState<string | null>(null);
  const [networkType, setNetworkType] = useState<string | null>(null);
  const [tripHistoryData, setTripHistoryData] =
    useState<TripHistoryDataResponse | null>(null);
  const [shiftTimes, setShiftTimes] = useState<ShiftTime[]>([]);

  // Search state
  const [selectedVendor, setSelectedVendor] = useState<{
    value: string;
    label: string;
  } | null>(null);
  const [selectedDate, setSelectedDate] = useState<string>(
    new Date().toISOString().split("T")[0]
  );
  const [selectedShiftTime, setSelectedShiftTime] = useState<{
    value: string;
    label: string;
  } | null>(null);
  const [selectedPickupDrop, setSelectedPickupDrop] = useState<{
    value: string;
    label: string;
  } | null>(null);

  // Grid state
  const [rowData, setRowData] = useState<TripHistoryRecord[]>([]);

  const employeeListModalRef = useRef<EmployeeListModalRef>(null);
  const [selectedRosterId, setSelectedRosterId] = useState<string>("");
  const [selectedLoginDateTime, setSelectedLoginDateTime] =
    useState<string>("");
  const [selectedTripType, setSelectedTripType] = useState<string>("");
  const employeeRosterModalRef = useRef<EmployeeRosterModalRef>(null);
  const escortModalRef = useRef<EscortModalRef>(null);
  const [selectedRouteId, setSelectedRouteId] = useState<string>("");

  const resetModalRef = useRef<ResetModalRef>(null);
  const [selectedResetRosterId, setSelectedResetRosterId] =
    useState<string>("");
    
  // Cab Allot Modal state
  const [selectedTripId, setSelectedTripId] = useState<number | undefined>(undefined);
  const [selectedActualCount, setSelectedActualCount] = useState<number>(0);
  const cabAllotModalRef = useRef<CabAllotModalRef>(null);

  // Add state to store retry data
  const [retryData, setRetryData] = useState<{
    rosterId: string;
    action: string;
    routeEscortId?: string;
  } | null>(null);

  const ScheduleCountRenderer: React.FC<ICellRendererParams> = (props) => {
    const handleClick = () => {
      setSelectedRosterId(props.data.ROSTER_ID.toString());
      setSelectedLoginDateTime(props.data.login_logout_time);
      setSelectedTripType(props.data.TRIP_TYPE);
      employeeListModalRef.current?.openModal();
    };

    return (
      <div className="d-flex align-items-center justify-content-center">
        <span className="me-2">{props.value}</span>
        <button
          className="btn btn-sm btn-primary"
          onClick={handleClick}
          style={{ padding: "0.1rem 0.5rem" }}
        >
          <i className="fas fa-users"></i>
        </button>
      </div>
    );
  };

  const RouteIdRenderer: React.FC<ICellRendererParams> = (props) => {
    const handleEditClick = async () => {
      try {
        setNetworkLoader(true);
        setError(null);
        // Clear previous roster ID first
        setSelectedRosterId("");

        const response = await getRosterDetails({
          roster_id: props.data.ROSTER_ID.toString(),
        });

        if (response.data.success) {
          // Set new roster ID after successful API call
          setSelectedRosterId(props.data.ROSTER_ID.toString());
          employeeRosterModalRef.current?.openModal();
        } else {
          setError("Failed to fetch roster details");
          setNetworkType("getRosterDetails");
          // Store retry data
          setRetryData({
            rosterId: props.data.ROSTER_ID.toString(),
            action: "getRosterDetails"
          });
          toast.error("Failed to fetch roster details");
        }
      } catch (error) {
        console.error("Error fetching roster details:", error);
        let errorMessage = "Failed to fetch roster details";
        if (axios.isAxiosError(error)) {
          const statusCode = error.response?.status;
          if (statusCode === 403 || statusCode === 401) {
            logout();
            errorMessage = "Your session has expired. Please log in again.";
          } else if (statusCode === 500) {
            errorMessage = "An error occurred while getting roster details. Please contact admin.";
          }
        }
        setError(errorMessage);
        setNetworkType("getRosterDetails");
        // Store retry data
        setRetryData({
          rosterId: props.data.ROSTER_ID.toString(),
          action: "getRosterDetails"
        });
        toast.error(errorMessage);
      } finally {
        setNetworkLoader(false);
      }
    };

    return (
      <div className="d-flex align-items-center">
        <span className="me-2">{`${props.data.TRIP_TYPE}-${props.value}`}</span>
        <button
          onClick={handleEditClick}
          className="btn btn-sm action-btn text-warning p-1"
          title="View Roster Details"
        >
          <i className="fas fa-edit text-success"></i>
        </button>
      </div>
    );
  };

  const EscortRenderer: React.FC<ICellRendererParams> = (props) => {
    const handleClick = () => {
      setSelectedRouteId(props.data.ROUTE_ID);
      setSelectedRosterId(props.data.ROSTER_ID.toString());
      escortModalRef.current?.openModal();
    };

    const handleReset = async () => {
      try {
        setNetworkLoader(true);
        setError(null);
        setNetworkType("resetEscort");
        
        const response = await resetEscort({
          route_escort_id: props.data.ROUTE_ESCORT_ID.toString(),
          selected_roster_id: props.data.ROSTER_ID.toString(),
        });

        if (response.data.success) {
          toast.success(response.data.message);
          // Refresh the grid data
          refreshGrid();
        } else {
          setError(response.data.message || "Failed to reset escort");
          // Store retry data
          setRetryData({
            rosterId: props.data.ROSTER_ID.toString(),
            action: "resetEscort",
            routeEscortId: props.data.ROUTE_ESCORT_ID.toString()
          });
          toast.error(response.data.message || "Failed to reset escort");
        }
      } catch (error) {
        console.error("Error resetting escort:", error);
        let errorMessage = "Failed to reset escort";
        if (axios.isAxiosError(error)) {
          const statusCode = error.response?.status;
          if (statusCode === 403 || statusCode === 401) {
            logout();
            errorMessage = "Your session has expired. Please log in again.";
          } else if (statusCode === 500) {
            errorMessage = "An error occurred while resetting escort. Please contact admin.";
          }
        }
        setError(errorMessage);
        setNetworkType("resetEscort");
        // Store retry data
        setRetryData({
          rosterId: props.data.ROSTER_ID.toString(),
          action: "resetEscort",
          routeEscortId: props.data.ROUTE_ESCORT_ID.toString()
        });
        toast.error(errorMessage);
      } finally {
        setNetworkLoader(false);
      }
    };

    return (
      <div
        className="d-flex align-items-center justify-content-center"
        style={{ height: "100%" }}
      >
        {props.data.ESCORTSTATUS === 1 &&
          (props.data.ESCORTALLOTSTATUS === 0 ? (
            <>
              <span className="me-2">YES</span>
              <button
                className="btn btn-sm btn-success"
                onClick={handleClick}
                style={{ padding: "0.1rem 0.5rem" }}
                title="Assign Escort"
              >
                <i className="fas fa-plus" style={{ padding: "0.1rem" }}></i>
              </button>
            </>
          ) : (
            <button
              className="btn btn-sm btn-danger"
              onClick={handleReset}
              style={{ padding: "0.1rem 0.5rem" }}
              title="Reset Escort"
            >
              <i className="fas fa-undo" style={{ padding: "0.1rem" }}></i>
            </button>
          ))}
      </div>
    );
  };

  const ActionCellRenderer: React.FC<ICellRendererParams> = (props) => {
    const handleCabAllotClick = () => {
      setSelectedTripId(props.data.ROSTER_ID);
      setSelectedActualCount(props.data.actual_count || 0);
      setSelectedTripType(props.data.TRIP_TYPE);
      cabAllotModalRef.current?.openModal();
    };

    const handleResetClick = () => {
      setSelectedResetRosterId(props.data.ROSTER_ID.toString());
      resetModalRef.current?.openModal();
    };

    return (
      <div
        className="d-flex align-items-center justify-content-center"
        style={{ height: "100%" }}
      >
        {props.data.showResetButton ? (
          <button
            className="btn btn-sm btn-danger"
            style={{ padding: "0.1rem 0.5rem" }}
            title="Reset"
            onClick={handleResetClick}
          >
            <i className="fas fa-undo" style={{ padding: "0.1rem" }}></i>
          </button>
        ) : props.data.showCabAllotButton ? (
          <button
            className="btn btn-sm btn-primary"
            style={{ padding: "0.1rem 0.5rem" }}
            onClick={handleCabAllotClick}
          >
            Cab Allot
          </button>
        ) : null}
      </div>
    );
  };
  const columnDefs = useMemo<ColDef[]>(() => {
    const baseColumns: ColDef[] = [
      // {
      //   headerName: "S.No",
      //   field: "ROSTER_ID",
      //   valueGetter: (params) => params.node?.rowIndex! + 1,
      // },
      {
        headerName: "Route Id",
        field: "ROUTE_ID",
        cellRenderer: RouteIdRenderer,
      },
      {
        headerName: "Pickup/Drop Location",
        field: "pickup_drop_loc",
        cellStyle: { color: "#198754" } as CellStyle,
      },
      {
        headerName: "In/Out",
        field: "login_logout",
      },
      {
        headerName: "Vendor Name",
        field: "vendor_name",
        cellStyle: { color: "#198754" } as CellStyle,
      },
      {
        headerName: "Vehicle No",
        field: "VEHICLE_REG_NO",
      },
      {
        headerName: "Driver Name",
        field: "DRIVERS_NAME",
        cellStyle: { color: "#198754" } as CellStyle,
      },
      {
        headerName: "Driver Mobile No",
        field: "DRIVER_MOBILE",
      },
      {
        headerName: "Schedule Count",
        field: "actual_count",
        cellRenderer: ScheduleCountRenderer,
        cellStyle: { textAlign: "center" } as CellStyle,
      },
      {
        headerName: "No Show Count",
        field: "PASSENGER_NOSHOW_COUNT",
        cellStyle: { textAlign: "center" } as CellStyle,
      },
      {
        headerName: "Traveled Count",
        field: "actual_count",
        cellStyle: { textAlign: "center" } as CellStyle,
      },
    ];

    // Check if any row has ESCORTSTATUS === 1
    const showEscortColumn = rowData.some((row) => row.ESCORTSTATUS === 1);

    if (showEscortColumn) {
      baseColumns.push({
        headerName: "Escort",
        field: "ESCORTSTATUS",
        cellRenderer: EscortRenderer,
        cellStyle: { textAlign: "center" } as CellStyle,
      });
    }

    baseColumns.push(
      {
        headerName: "Close Time",
        field: "actual_end_time",
      },
      {
        headerName: "Status",
        field: "tripstatus",
        cellStyle: (params) =>
          ({
            color:
              params.value === "TripClosed"
                ? "#198754"
                : params.value === "Trip created"
                ? "#dc3545"
                : params.value === "Cab Delay and Trip Closed"
                ? "#dc3545"
                : "inherit",
          } as CellStyle),
      },
      {
        headerName: "Action",
        field: "ROSTER_STATUS",
        cellRenderer: ActionCellRenderer,
        cellStyle: { textAlign: "center" } as CellStyle,
      }
    );

    return baseColumns;
  }, [rowData]); // Add rowData as a dependency

  // const defaultColDef = useMemo(
  //   () => ({
  //     sortable: true,
  //     filter: true,
  //     resizable: true,
  //   }),
  //   []
  // );
  const defaultColDef = useMemo<ColDef>(
    () => ({
      filter: "agTextColumnFilter",
      filterParams: {
        filterOptions: ["contains"],
        maxNumConditions: 1,
      },
      floatingFilter: true,
      sortable: true,
    }),
    []
  );

  useEffect(() => {
    fetchTripHistoryData();
  }, []);

  useEffect(() => {
    if (tripHistoryData?.category && tripHistoryData.category.length > 0) {
      const pickupOption = tripHistoryData.category.find(
        (cat) => cat.value === "P"
      );
      if (pickupOption) {
        setSelectedPickupDrop({
          value: pickupOption.value,
          label: pickupOption.name,
        });
      }
    }
  }, [tripHistoryData]);

  useEffect(() => {
    if (selectedPickupDrop && selectedDate) {
      fetchShiftTimes();
    }
  }, [selectedPickupDrop, selectedDate]);

  const fetchShiftTimes = async () => {
    if (!selectedPickupDrop || !selectedDate) return;

    try {
      setNetworkLoader(true);
      setError(null);
      const response = await getTripHistoryShiftTime({
        selected_trip_type: selectedPickupDrop.value,
        selected_date: selectedDate,
      });

      if (response.data.success) {
        setShiftTimes(response.data.shift_time);
        // Set selected shift time to the first option from API if available, else null
        const firstShift = response.data.shift_time[0];
        if (firstShift) {
          if ('ALL' in firstShift) {
            setSelectedShiftTime({ value: firstShift.ALL as string, label: firstShift.ALL as string });
          } else {
            setSelectedShiftTime({ value: firstShift.login_time, label: firstShift.login_time });
          }
        } else {
          setSelectedShiftTime(null);
        }
        // Clear any existing retry data on successful fetch
        setRetryData(null);
      }
    } catch (error) {
      let errorMessage = "Failed to fetch shift times. Please try again.";
      if (axios.isAxiosError(error)) {
        const statusCode = error.response?.status;
        if (statusCode === 403 || statusCode === 401) {
          logout();
          errorMessage = "Your session has expired. Please log in again.";
        }
      }
      setError(errorMessage);
      setNetworkType("getTripHistoryShiftTime");
      toast.error(errorMessage);
      setShiftTimes([]);
    } finally {
      setNetworkLoader(false);
    }
  };

  const fetchTripHistoryData = async () => {
    setNetworkLoader(true);
    try {
      const response = await getTripHistoryData();
      setTripHistoryData(response.data);
      // Clear any existing retry data on successful data fetch
      setRetryData(null);
    } catch (error) {
      let errorMessage =
        "An error occurred while getting trip history data. Please try again.";
      if (axios.isAxiosError(error)) {
        const statusCode = error.response?.status;
        if (statusCode === 403 || statusCode === 401) {
          logout();
          errorMessage = "Your session has expired. Please log in again.";
        } else if (statusCode === 500) {
          errorMessage =
            "An error occurred while getting trip history data. Please contact admin.";
        }
      }
      setError(errorMessage);
      setNetworkType("getTripHistoryData");
      console.error("Error getting trip history data:", error);
    } finally {
      setNetworkLoader(false);
    }
  };

  const handleVendorChange = (selectedOption: any) => {
    setSelectedVendor(selectedOption);
  };

  const handleDateChange = (date: string | null) => {
    setSelectedDate(date || "");
  };

  const handleShiftTimeChange = (selectedOption: any) => {
    setSelectedShiftTime(selectedOption);
  };

  const handlePickupDropChange = (selectedOption: any) => {
    setSelectedPickupDrop(selectedOption);
  };

  const handleSearch = async () => {
    if (!selectedPickupDrop || !selectedDate) {
      toast.error("Please select pickup/drop and date before searching.");
      return;
    }

    try {
      setLoading(true);
      setError(null);
      const response = await getTripHistoryFilter({
        selected_trip_type: selectedPickupDrop.value,
        selected_date: selectedDate,
        selected_shift_time: selectedShiftTime?.value || "ALL",
        selected_vendor: selectedVendor?.value || "ALL",
      });

      console.log("Trip History Filter Response:", response.data);

      if (response.data.success) {
        setRowData(response.data.result || []);
        setShowGrid(true);
        // Clear any existing retry data on successful search
        setRetryData(null);
      } else {
        toast.error(response.data.message || "Failed to fetch search results.");
      }
    } catch (error) {
      console.error("Search failed", error);
      let errorMessage = "Failed to fetch search results. Please try again.";
      if (axios.isAxiosError(error)) {
        const statusCode = error.response?.status;
        if (statusCode === 403 || statusCode === 401) {
          logout();
          errorMessage = "Your session has expired. Please log in again.";
        }
      }
      setError(errorMessage);
      setNetworkType("getTripHistoryFilter");
      toast.error(errorMessage);
    } finally {
      setLoading(false);
    }
  };

  const onBtnExport = () => {
    if (gridRef.current) {
      gridRef.current.api.exportDataAsCsv({
        fileName: `trip_history_${selectedDate}.csv`,
      });
    }
  };

  // No-op for onGridReady, let AG Grid use explicit column widths
  const onGridReady = () => {};

  const handleTryingAgainClick = async () => {
    setError(null);
    setNetworkLoader(true);
    
    try {
      switch (networkType) {
        case "getTripHistoryData":
          await fetchTripHistoryData();
          break;
        case "getRosterDetails":
          if (retryData && retryData.action === "getRosterDetails") {
            const response = await getRosterDetails({
              roster_id: retryData.rosterId,
            });
            
            if (response.data.success) {
              setSelectedRosterId(retryData.rosterId);
              employeeRosterModalRef.current?.openModal();
              // Clear retry data on success
              setRetryData(null);
            } else {
              setError("Failed to fetch roster details");
              toast.error("Failed to fetch roster details");
            }
          }
          break;
        case "getTripHistoryShiftTime":
          await fetchShiftTimes();
          break;
        case "getTripHistoryFilter":
          await handleSearch();
          break;
        case "resetEscort":
          // For resetEscort, retry the actual API call
          if (retryData && retryData.action === "resetEscort" && retryData.routeEscortId) {
            const response = await resetEscort({
              route_escort_id: retryData.routeEscortId,
              selected_roster_id: retryData.rosterId,
            });
            
            if (response.data.success) {
              toast.success(response.data.message);
              // Refresh the grid data
              await refreshGrid();
              // Clear retry data on success
              setRetryData(null);
            } else {
              setError(response.data.message || "Failed to reset escort");
              toast.error(response.data.message || "Failed to reset escort");
            }
          }
          break;
        default:
          console.error("Unknown network type");
      }
    } catch (error) {
      console.error("Retry failed:", error);
      let errorMessage = "Retry failed. Please try again.";
      if (axios.isAxiosError(error)) {
        const statusCode = error.response?.status;
        if (statusCode === 403 || statusCode === 401) {
          logout();
          errorMessage = "Your session has expired. Please log in again.";
        }
      }
      setError(errorMessage);
      toast.error(errorMessage);
    } finally {
      setNetworkLoader(false);
    }
  };

  const clearSelectedRosterId = () => {
    setSelectedRosterId("");
    // Clear retry data when modal is closed
    setRetryData(null);
  };

  const refreshGrid = async () => {
    try {
      setLoading(true);
      const response = await getTripHistoryFilter({
        selected_trip_type: selectedPickupDrop?.value || "",
        selected_date: selectedDate,
        selected_shift_time: selectedShiftTime?.value || "ALL",
        selected_vendor: selectedVendor?.value || "ALL",
      });

      if (response.data.success) {
        setRowData(response.data.result || []);
      } else {
        toast.error(response.data.message || "Failed to refresh grid data.");
      }
    } catch (error) {
      console.error("Grid refresh failed", error);
      toast.error("Failed to refresh grid data.");
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="page-container">
      <div id="triphistoryone" className={`${error ? "d-block" : "d-none"}`}>
        <div className="d-flex flex-column justify-content-center align-items-center">
          <div className="text-center mb-4">
            <div className="alert alert-danger" role="alert">
              {error}
            </div>
          </div>
          <div>
            <button className="btn btn-danger" onClick={handleTryingAgainClick}>
              Try Again
            </button>
          </div>
        </div>
      </div>
      <div id="triphistorytwo" className={`${error ? "d-none" : "d-block"}`}>
        <div className="row align-items-center mx-1">
          <div className="col-12">
            <h1 className="h5 fw-semibold text-body">Trip History</h1>
            <p className="mt-2 small text-muted">
              A list of all trip history details.
            </p>
          </div>
        </div>
        <div className="row g-3 mx-1 mb-3">
          <div className="col-12 col-md-6 col-lg">
            <ReactSelect
              options={
                tripHistoryData?.category.map((cat) => ({
                  value: cat.value,
                  label: cat.name,
                })) || []
              }
              placeholder="Select Pickup/Drop"
              onChange={handlePickupDropChange}
              value={selectedPickupDrop}
            />
          </div>
          <div className="col-12 col-md-6 col-lg">
            <FlatpickrThree
              onChange={handleDateChange}
              placeholder="Select Date"
              initialDate={selectedDate}
            />
          </div>
          <div className="col-12 col-md-6 col-lg">
            <ReactSelect
              options={
                shiftTimes
                  ? shiftTimes.map((shift): { value: string; label: string } => {
                      if ('ALL' in shift) {
                        return { value: shift.ALL as string, label: shift.ALL as string };
                      }
                      return { value: shift.login_time, label: shift.login_time };
                    })
                  : []
              }
              placeholder="Select Shift Time"
              onChange={handleShiftTimeChange}
              value={selectedShiftTime}
            />
          </div>
          <div className="col-12 col-md-6 col-lg">
            <ReactSelect
              options={
                tripHistoryData?.vendor_list
                  ? tripHistoryData.vendor_list.map((vendor): { value: string; label: string } => {
                      if ('ALL' in vendor) {
                        return { value: vendor.ALL as string, label: vendor.ALL as string };
                      }
                      return { value: vendor.VENDOR_ID.toString(), label: vendor.NAME };
                    })
                  : []
              }
              placeholder="Select Vendor"
              onChange={handleVendorChange}
              value={selectedVendor}
            />
          </div>
          <div className="col-12 col-md-6 col-lg-auto d-flex gap-2">
            <button
              className="btn btn-primary flex-fill"
              onClick={handleSearch}
              disabled={loading}
            >
              {loading ? "Searching..." : "Search"}
            </button>
            {showGrid && (
              <button
                type="button"
                className="btn btn-primary flex-fill"
                onClick={onBtnExport}
              >
                Export CSV
              </button>
            )}
          </div>
        </div>

        {showGrid && (
          <div className="px-2">
            <div
              className="ag-theme-quartz"
              style={{ 
                height: 600, 
                width: "100%",
                minWidth: "300px"
              }}
            >
              <AgGridReact
                ref={gridRef}
                rowData={rowData}
                columnDefs={columnDefs}
                defaultColDef={defaultColDef}
                onGridReady={onGridReady}
                pagination={true}
                paginationPageSize={20}
                loadingOverlayComponent={LoadingOverlay}
                noRowsOverlayComponent={NoRowsOverlay}
              />
            </div>
          </div>
        )}

        <CabAllotModal
          ref={cabAllotModalRef}
          id="cabAllotModal"
          title="Cab Allotment"
          tripId={selectedTripId}
          onCabAlloted={refreshGrid}
          actualCount={selectedActualCount}
          tripType={selectedTripType}
        />

        <EmployeeListModal
          ref={employeeListModalRef}
          id="employeeListModal"
          title="Clubbing"
          rosterId={selectedRosterId}
          loginDateTime={selectedLoginDateTime}
          tripType={selectedTripType}
        />

        <EmployeeRosterModal
          ref={employeeRosterModalRef}
          id="employeeRosterModal"
          title="Employee Roster Details"
          rosterId={selectedRosterId}
          onClose={clearSelectedRosterId}
        />

        <EscortModal
          ref={escortModalRef}
          id="escortModal"
          title="Action for Escort"
          rosterId={selectedRosterId}
          routeId={selectedRouteId}
          routeEscortId={
            rowData.find((row) => row.ROUTE_ID === selectedRouteId)
              ?.ROUTE_ESCORT_ID || 0
          }
          onSuccess={refreshGrid}
        />

        <ResetModal
          ref={resetModalRef}
          id="resetModal"
          title="Action for Reset"
          rosterId={selectedResetRosterId}
          onSuccess={refreshGrid}
        />

        {networkLoader && (
          <>
            <div className="custom-overlay show"></div>
            <div className="position-absolute top-50 start-50 translate-middle custom-loading-spinner">
              <div className="d-flex flex-column align-items-center">
                <div className="spinner-border text-primary" role="status" />
                <div className="mt-2 text-primary fw-bold">Loading...</div>
              </div>
            </div>
          </>
        )}
      </div>
    </div>
  );
};

export default TripHistory;
